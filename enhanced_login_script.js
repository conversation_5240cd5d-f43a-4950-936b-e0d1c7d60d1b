// 前置脚本 Pre-script - 增强版登录脚本

// ==========================================================================
// BEGIN: Embedded Pure JavaScript SHA1 Implementation
// (Source: Adapted from https://github.com/emn178/js-sha1)
// ==========================================================================
function sha1(message) {
    function rotateLeft(n, s) {
        return (n << s) | (n >>> (32 - s));
    };

    function cvtHex(val) {
        var str = '';
        var i;
        var v;
        for (i = 7; i >= 0; i--) {
            v = (val >>> (i * 4)) & 0x0f;
            str += v.toString(16);
        }
        return str;
    };

    var blockstart;
    var i, j;
    var W = new Array(80);
    var H0 = 0x67452301;
    var H1 = 0xEFCDAB89;
    var H2 = 0x98BADCFE;
    var H3 = 0x10325476;
    var H4 = 0xC3D2E1F0;
    var A, B, C, D, E;
    var temp;

    // UTF-8 encoding
    message = unescape(encodeURIComponent(message));
    var msgLen = message.length;

    var word_array = [];
    for (i = 0; i < msgLen - 3; i += 4) {
        j = message.charCodeAt(i) << 24 | message.charCodeAt(i + 1) << 16 |
            message.charCodeAt(i + 2) << 8 | message.charCodeAt(i + 3);
        word_array.push(j);
    }

    switch (msgLen % 4) {
        case 0:
            i = 0x080000000;
            break;
        case 1:
            i = message.charCodeAt(msgLen - 1) << 24 | 0x0800000;
            break;
        case 2:
            i = message.charCodeAt(msgLen - 2) << 24 | message.charCodeAt(msgLen - 1) << 16 | 0x08000;
            break;
        case 3:
            i = message.charCodeAt(msgLen - 3) << 24 | message.charCodeAt(msgLen - 2) << 16 | message.charCodeAt(msgLen - 1) << 8 | 0x80;
            break;
    }

    word_array.push(i);

    while ((word_array.length % 16) != 14) {
        word_array.push(0);
    }

    word_array.push(msgLen >>> 29);
    word_array.push((msgLen << 3) & 0x0ffffffff);

    for (blockstart = 0; blockstart < word_array.length; blockstart += 16) {
        for (i = 0; i < 16; i++) {
            W[i] = word_array[blockstart + i];
        }
        for (i = 16; i <= 79; i++) {
            W[i] = rotateLeft(W[i - 3] ^ W[i - 8] ^ W[i - 14] ^ W[i - 16], 1);
        }

        A = H0;
        B = H1;
        C = H2;
        D = H3;
        E = H4;

        for (i = 0; i <= 19; i++) {
            temp = (rotateLeft(A, 5) + ((B & C) | (~B & D)) + E + W[i] + 0x5A827999) & 0x0ffffffff;
            E = D;
            D = C;
            C = rotateLeft(B, 30);
            B = A;
            A = temp;
        }

        for (i = 20; i <= 39; i++) {
            temp = (rotateLeft(A, 5) + (B ^ C ^ D) + E + W[i] + 0x6ED9EBA1) & 0x0ffffffff;
            E = D;
            D = C;
            C = rotateLeft(B, 30);
            B = A;
            A = temp;
        }

        for (i = 40; i <= 59; i++) {
            temp = (rotateLeft(A, 5) + ((B & C) | (B & D) | (C & D)) + E + W[i] + 0x8F1BBCDC) & 0x0ffffffff;
            E = D;
            D = C;
            C = rotateLeft(B, 30);
            B = A;
            A = temp;
        }

        for (i = 60; i <= 79; i++) {
            temp = (rotateLeft(A, 5) + (B ^ C ^ D) + E + W[i] + 0xCA62C1D6) & 0x0ffffffff;
            E = D;
            D = C;
            C = rotateLeft(B, 30);
            B = A;
            A = temp;
        }

        H0 = (H0 + A) & 0x0ffffffff;
        H1 = (H1 + B) & 0x0ffffffff;
        H2 = (H2 + C) & 0x0ffffffff;
        H3 = (H3 + D) & 0x0ffffffff;
        H4 = (H4 + E) & 0x0ffffffff;
    }

    temp = cvtHex(H0) + cvtHex(H1) + cvtHex(H2) + cvtHex(H3) + cvtHex(H4);
    return temp.toLowerCase();
}
// ==========================================================================
// END: Embedded Pure JavaScript SHA1 Implementation
// ==========================================================================

// ==========================================================================
// BEGIN: 预置账号配置
// ==========================================================================
const PRESET_ACCOUNTS = {
    'a': {
        username: 'admin',
        password: 'zz123456',
        description: '超管'
    },
    'f': {
        username: '<EMAIL>',
        password: 'aa123456',
        description: '分销商'
    },
    'g': {
        username: '<EMAIL>',
        password: 'qq123456',
        description: '供应商'
    }
};

// 默认登录接口URL（备用）
const DEFAULT_LOGIN_API_URL = 'https://distribution-pr.ehengjian.com/prod-api/auth/login';

// 登录接口路径
const LOGIN_API_PATH = '/auth/login';
// ==========================================================================
// END: 预置账号配置
// ==========================================================================

// ==========================================================================
// BEGIN: 环境配置获取函数
// ==========================================================================
function getLoginApiUrl() {
    try {
        // 获取当前环境名称
        const envName = apt.environment.getName();
        console.log(`当前环境名称: ${envName}`);

        // 获取当前环境的URL前缀
        const preUrl = apt.environment.getPreUrl();
        console.log(`当前环境URL前缀: ${preUrl}`);

        if (preUrl) {
            // 确保URL前缀以/结尾，登录路径以/开头
            const baseUrl = preUrl.endsWith('/') ? preUrl.slice(0, -1) : preUrl;
            const loginUrl = baseUrl + LOGIN_API_PATH;
            console.log(`构建的登录接口URL: ${loginUrl}`);
            return loginUrl;
        } else {
            console.log('未获取到环境URL前缀，使用默认登录接口URL');
            return DEFAULT_LOGIN_API_URL;
        }

    } catch (error) {
        console.error('获取环境配置时发生错误:', error);
        console.log('使用默认登录接口URL');
        return DEFAULT_LOGIN_API_URL;
    }
}
// ==========================================================================
// END: 环境配置获取函数
// ==========================================================================

// ==========================================================================
// BEGIN: 自动登录获取Token函数
// ==========================================================================
async function autoLogin(loginType) {
    try {
        console.log(`开始自动登录，登录类型: ${loginType}`);

        // 获取预置账号信息
        const account = PRESET_ACCOUNTS[loginType];
        if (!account) {
            throw new Error(`未找到登录类型 '${loginType}' 对应的预置账号`);
        }

        console.log(`使用预置账号: ${account.username} (${account.description})`);

        // 动态获取登录接口URL
        const loginApiUrl = getLoginApiUrl();
        console.log(`使用登录接口URL: ${loginApiUrl}`);

        // 计算密码的SHA1哈希
        const hashedPassword = sha1(account.password);
        console.log(`密码SHA1哈希: ${hashedPassword}`);

        // 构建登录请求体
        const loginRequestBody = {
            username: account.username,
            password: hashedPassword,
            code: "",
            uuid: ""
        };

        console.log('发送登录请求...');

        // 发送登录请求
        const loginResponse = await apt.request.send({
            url: loginApiUrl,
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Accept': '*/*',
                'User-Agent': 'PostmanRuntime-ApipostRuntime/1.1.0'
            },
            body: JSON.stringify(loginRequestBody)
        });

        console.log('登录响应状态:', loginResponse.status);
        console.log('登录响应体:', loginResponse.body);

        // 解析响应
        const responseData = JSON.parse(loginResponse.body);

        if (loginResponse.status === 200 && responseData.code === 200) {
            // 从data.token中获取token（根据实际响应结构）
            const token = responseData.data?.token;
            if (token) {
                console.log(`登录成功，获取到token: ${token}`);

                // 拼接Bearer前缀并设置Authorization头
                const authHeader = `Bearer ${token}`;
                apt.setRequestHeader('Authorization', authHeader);
                console.log(`已设置Authorization头: ${authHeader}`);

                return token;
            } else {
                throw new Error('登录响应中未找到data.token字段');
            }
        } else {
            throw new Error(`登录失败: ${responseData.msg || '未知错误'}`);
        }

    } catch (error) {
        console.error('自动登录过程中发生错误:', error);
        throw error;
    }
}
// ==========================================================================
// END: 自动登录获取Token函数
// ==========================================================================

// ==========================================================================
// BEGIN: 主脚本逻辑
// ==========================================================================
(async function() {
    try {
        console.log('=== 增强版登录脚本开始执行 ===');

        // 1. 从request对象中获取loginType头部
        let loginType = null;

        console.log('开始检测loginType头部...');
        console.log('request对象:', JSON.stringify(request, null, 2));

        // 方法1: 从request.headers获取
        try {
            if (request && request.headers) {
                loginType = request.headers['loginType'] ||
                           request.headers['logintype'] ||
                           request.headers['LoginType'] ||
                           request.headers['LOGINTYPE'];
                if (loginType) {
                    console.log(`从request.headers获取到登录类型: ${loginType}`);
                }
            }
        } catch (e) {
            console.log('从request.headers获取失败:', e.message);
        }

        // 方法2: 从request.request_headers获取
        if (!loginType) {
            try {
                if (request && request.request_headers) {
                    loginType = request.request_headers['loginType'] ||
                               request.request_headers['logintype'] ||
                               request.request_headers['LoginType'] ||
                               request.request_headers['LOGINTYPE'];
                    if (loginType) {
                        console.log(`从request.request_headers获取到登录类型: ${loginType}`);
                    }
                }
            } catch (e) {
                console.log('从request.request_headers获取失败:', e.message);
            }
        }

        // 方法3: 遍历所有可能的头部字段查找
        if (!loginType) {
            try {
                const headerSources = [
                    request?.headers,
                    request?.request_headers,
                    apt?.request?.headers
                ];

                for (const headers of headerSources) {
                    if (headers && typeof headers === 'object') {
                        for (const [key, value] of Object.entries(headers)) {
                            if (key.toLowerCase() === 'logintype') {
                                loginType = value;
                                console.log(`通过遍历找到登录类型头 ${key}: ${loginType}`);
                                break;
                            }
                        }
                        if (loginType) break;
                    }
                }
            } catch (e) {
                console.log('遍历头部失败:', e.message);
            }
        }

        if (loginType) {
            console.log(`检测到loginType头: ${loginType}`);

            // 2. 执行自动登录
            await autoLogin(loginType);
            console.log('自动登录完成');

        } else {
            console.log('未检测到loginType头，执行原有的密码加密逻辑...');

            // 3. 原有的密码加密逻辑（保持向后兼容）
            try {
                // 安全地访问请求体
                let requestBodyRaw = null;
                if (apt.request && apt.request.body && apt.request.body.raw !== undefined) {
                    requestBodyRaw = apt.request.body.raw;
                }

                console.log("原始请求体:", requestBodyRaw);

                if (requestBodyRaw) {
                    let requestBodyObj = JSON.parse(requestBodyRaw);
                    console.log("解析后的请求体对象:", requestBodyObj);

                    const originalPassword = requestBodyObj.password;

                    if (originalPassword && typeof originalPassword === 'string') {
                        console.log("从请求体中提取的原始密码:", originalPassword);

                        const sha1Password = sha1(originalPassword);
                        console.log("计算得到的 SHA1 密码:", sha1Password);

                        requestBodyObj.password = sha1Password;
                        console.log("用 SHA1 密码更新后的请求体对象:", requestBodyObj);

                        let modifiedBodyRaw = JSON.stringify(requestBodyObj);
                        console.log("序列化后的请求体字符串:", modifiedBodyRaw);

                        apt.request.body.raw = modifiedBodyRaw;
                        console.log("成功更新请求体，使用加密后的密码。");

                    } else {
                        console.log("请求体中未找到有效的 'password' 字段或其值不是字符串，未进行加密处理。");
                    }
                } else {
                    console.log("请求体为空或不存在，未进行修改。");
                }
            } catch (bodyError) {
                console.error("处理请求体时发生错误:", bodyError);
                console.log("跳过密码加密处理，继续执行请求。");
            }
        }

        console.log('=== 增强版登录脚本执行完成 ===');

    } catch (error) {
        console.error("脚本执行过程中发生错误:", error);
        console.error("错误详情:", error.message, error.stack);
        // 可选：如果希望错误阻止请求发送，取消此行注释
        // throw error;
    }
})();
// ==========================================================================
// END: 主脚本逻辑
// ==========================================================================
