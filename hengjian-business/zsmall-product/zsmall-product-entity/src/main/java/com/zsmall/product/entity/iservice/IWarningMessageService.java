package com.zsmall.product.entity.iservice;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.core.utils.MapstructUtils;
import com.hengjian.common.core.utils.StringUtils;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.zsmall.product.entity.domain.WarningMessage;
import com.zsmall.product.entity.domain.bo.warningMessage.WarningMessageBo;
import com.zsmall.product.entity.domain.vo.warningMessage.WarningMessageVo;
import com.zsmall.product.entity.mapper.WarningMessageMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 预警消息Service接口
 *
 * <AUTHOR>
 * @date 2025-01-28
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class IWarningMessageService extends ServiceImpl<WarningMessageMapper, WarningMessage> {
    @Resource
    private WarningMessageMapper baseMapper;
    /**
     * 查询预警消息
     */
    public WarningMessageVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询预警消息列表
     */
    public TableDataInfo<WarningMessageVo> queryPageList(WarningMessageBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<WarningMessage> lqw = buildQueryWrapper(bo);
        Page<WarningMessageVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询预警消息列表
     */
    public List<WarningMessageVo> queryList(WarningMessageBo bo) {
        LambdaQueryWrapper<WarningMessage> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<WarningMessage> buildQueryWrapper(WarningMessageBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<WarningMessage> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getTitle()), WarningMessage::getTitle, bo.getTitle());
        lqw.eq(StringUtils.isNotBlank(bo.getTenantType()), WarningMessage::getTenantType, bo.getTenantType());
        lqw.eq(ObjectUtil.isNotNull(bo.getBusinessType()), WarningMessage::getBusinessType, bo.getBusinessType());
        lqw.orderByDesc(WarningMessage::getCreateTime);
        return lqw;
    }

    /**
     * 新增预警消息
     */
    public Boolean insertByBo(WarningMessageBo bo) {
        WarningMessage add = MapstructUtils.convert(bo, WarningMessage.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改预警消息
     */
    public Boolean updateByBo(WarningMessageBo bo) {
        WarningMessage update = MapstructUtils.convert(bo, WarningMessage.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(WarningMessage entity) {
        // TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除预警消息
     */
    public Boolean deleteWithValidByIds(Collection<Long> ids) {
        return baseMapper.deleteWarningMessage(ids) > 0;
    }

    /**
     * 根据租户类型查询未读消息数量
     */
    public Long countUnreadMessages() {
        return baseMapper.countUnreadMessages();
    }

    /**
     * 批量标记消息为已读
     */
    public Boolean batchMarkAsRead(List<Long> ids) {
        return baseMapper.batchMarkAsRead(ids) > 0;
    }

    /**
     * 根据租户类型查询消息列表
     */
    public List<WarningMessageVo> getMessagesByTenantType(String tenantType) {
        return baseMapper.selectByTenantType(tenantType);
    }

    /**
     * 创建库存预警消息
     */
    public Boolean createStockWarningMessage(String title, String content, String tenantType, Integer businessType) {
        WarningMessage warningMessage = new WarningMessage();
        warningMessage.setTitle(title);
        warningMessage.setContent(content);
        warningMessage.setTenantType(tenantType);
        warningMessage.setBusinessType(businessType);
        return baseMapper.insert(warningMessage) > 0;
    }


}
